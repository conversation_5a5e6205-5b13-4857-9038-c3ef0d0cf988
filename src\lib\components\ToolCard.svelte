<script lang="ts">
	import { ExternalLink, DollarSign, Gift, Tags, CheckCircle } from 'lucide-svelte';
	
	interface ToolCardProps {
		name: string;
		description: string;
		category: string;
		price: string;
		imageColor?: string;
		categoryColor?: string;
		href?: string;
		slug?: string;
		thumbnailUrl?: string;
		categorySlug?: string;
		pricingSlug?: string;
	}

	let {
		name,
		description,
		category,
		price,
		imageColor = 'e0e7ff/3730a3',
		categoryColor = 'blue',
		href,
		slug = 'tool-slug',
		thumbnailUrl,
		categorySlug,
		pricingSlug
	}: ToolCardProps = $props();

	// Set href based on slug if not provided
	if (!href) {
		href = `/ai/${slug}`;
	}
	
	const pricingConfig: Record<string, { icon: any; text: string; color: string }> = {
		'Freemium': { icon: Tags, text: 'Freemium', color: 'text-green-600' },
		'Free Trial': { icon: CheckCircle, text: 'Free Trial', color: 'text-green-600' },
		'Paid': { icon: DollarSign, text: 'Paid', color: 'text-green-600' },
		'Free': { icon: Gift, text: 'Free', color: 'text-green-600' }
	};

	const categoryColors = {
		'blue': 'bg-blue-100 text-blue-800',
		'orange': 'bg-orange-100 text-orange-800',
		'yellow': 'bg-yellow-100 text-yellow-800',
		'pink': 'bg-pink-100 text-pink-800',
		'teal': 'bg-teal-100 text-teal-800',
		'red': 'bg-red-100 text-red-800',
		'gray': 'bg-gray-100 text-gray-800',
		'lime': 'bg-lime-100 text-lime-800',
		'purple': 'bg-purple-100 text-purple-800'
	};

	const priceConfig = pricingConfig[price] || pricingConfig['Free'];
	const catColorClass = categoryColors[categoryColor as keyof typeof categoryColors] || categoryColors.gray;
	const IconComponent = priceConfig.icon;

	// Generate href if not provided
	const toolHref = href || `/ai/${slug}`;
</script>

<div class="bg-white rounded-sm shadow-sm border border-gray-200 flex flex-col overflow-hidden transform transition-all duration-300 hover:shadow-md hover:-translate-y-1">
	<a href={toolHref}>
		<img
			src={thumbnailUrl || `https://placehold.co/400x225/${imageColor}?text=${encodeURIComponent(name)}`}
			alt="{name} - AI tool for {category || 'productivity'}"
			class="w-full aspect-video object-cover"
			loading="lazy"
			decoding="async"
		>
	</a>

	<div class="p-4 flex flex-col flex-grow">
		<h3 class="font-bold text-lg mb-1 text-blue-900">
			<a href={toolHref}>{name}</a>
		</h3>
		<p class="text-sm text-gray-600 flex-grow leading-relaxed line-clamp-3">{description}</p>

		<div class="my-3 flex flex-wrap gap-2">
			{#if category}
				<span class="{catColorClass} text-xs font-medium px-2.5 py-0.5 rounded-full">
					{#if categorySlug}
						<a href="/category/{categorySlug}" class="hover:underline">
							{category}
						</a>
					{:else}
						{category}
					{/if}
				</span>
			{/if}
			{#if price}
				<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
					{#if pricingSlug}
						<a href="/pricing/{pricingSlug}" class="hover:underline flex items-center">
							<IconComponent class="w-3 h-3 mr-1" />
							{priceConfig.text}
						</a>
					{:else}
						<span class="flex items-center">
							<IconComponent class="w-3 h-3 mr-1" />
							{priceConfig.text}
						</span>
					{/if}
				</span>
			{/if}
		</div>

		<div class="border-t border-gray-100 pt-3 mt-auto flex justify-between items-center">
			<a
				href={toolHref}
				class="text-sm font-semibold text-blue-900 hover:underline group flex items-center"
			>
				Visit
				<ExternalLink class="w-4 h-4 ml-1 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
			</a>
		</div>
	</div>
</div>
