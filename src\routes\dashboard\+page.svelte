<script lang="ts">
	import Head from '$lib/components/Head.svelte';
	import { Check, X, ExternalLink } from 'lucide-svelte';
	import { PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Handle approval/rejection
	async function handleItemAction(itemId: number, action: 'approve' | 'reject') {
		try {
			const response = await fetch('/api/ManageItem', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					itemId,
					action
				})
			});

			const result = await response.json();

			if (response.ok) {
				// Reload the page to refresh the list
				window.location.reload();
			} else {
				alert(result.error || 'Error processing request');
			}
		} catch (error) {
			console.error('Error:', error);
			alert('Error processing request');
		}
	}


</script>

<Head
	title="Dashboard - Pending Items"
	description="Manage pending AI tool submissions"
	url="{PUBLIC_SITE_URL}/dashboard"
/>

<div class="container mx-auto px-4 sm:px-6 py-8">
	<div class="mb-8">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6">Dashboard</h1>
		<p class="text-lg text-gray-600">
			Review and manage pending AI tool submissions.
		</p>
	</div>

	{#if data.items.length === 0}
		<div class="text-center py-12 bg-white border border-gray-200 rounded-lg shadow-sm">
			<div class="p-6">
				<h2 class="text-2xl font-semibold text-gray-600 mb-4">No Pending Items</h2>
				<p class="text-gray-500">All submissions have been reviewed.</p>
			</div>
		</div>
	{:else}
		<!-- Items List -->
		<div class="space-y-6 mb-12">
			{#each data.items as item}
				<div class="bg-white border border-gray-200 shadow-sm rounded-lg">
					<div class="p-6 border-b border-gray-100">
						<div class="flex justify-between items-start">
							<div>
								<h3 class="text-xl text-blue-900 font-semibold">{item.item_name}</h3>
								<p class="text-gray-600 mt-1">{getMetaValue(item, 'description')}</p>
							</div>
							<span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
								Pending
							</span>
						</div>
					</div>
					<div class="p-6">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Details</h4>
								<div class="space-y-2 text-sm">
									<div><strong>Website:</strong>
										<a href={item.item_url} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline inline-flex items-center gap-1">
											{item.item_url}
											<ExternalLink class="w-3 h-3" />
										</a>
									</div>
									<div><strong>Submitted:</strong> {new Date(item.item_created_at * 1000).toLocaleDateString()}</div>
								</div>
							</div>
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Description</h4>
								<p class="text-sm text-gray-600 line-clamp-3">{getMetaValue(item, 'content')}</p>
							</div>
						</div>

						<div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-100">
							<button
								onclick={() => handleItemAction(item.item_id, 'reject')}
								class="px-4 py-2 text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors flex items-center gap-2"
							>
								<X class="w-4 h-4" />
								Reject
							</button>
							<button
								onclick={() => handleItemAction(item.item_id, 'approve')}
								class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors flex items-center gap-2"
							>
								<Check class="w-4 h-4" />
								Approve
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Total Info -->
		<div class="text-center mt-8 text-sm text-gray-600">
			Showing {data.total} pending items total
		</div>
	{/if}
</div>
