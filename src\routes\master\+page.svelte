<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  // UI components removed - using plain HTML and Tailwind
  import type { User, Item, Term, Option, ItemMeta, TermMeta } from '$lib/types/tables.js';

  let isAuthenticated = false;
  let password = '';
  let token = '';
  let loginError = '';

  let users: User[] = [];
  let items: Item[] = [];
  let terms: Term[] = [];
  let options: Option[] = [];
  let itemMetas: ItemMeta[] = [];
  let termMetas: TermMeta[] = [];

  let loading = false;
  let activeTab = 'users';
  let dataError = '';
  let showSetupMessage = false;

  // Form states
  let showAddUserDialog = false;
  let showAddItemDialog = false;
  let showAddTermDialog = false;
  let showAddOptionDialog = false;
  let showAddItemMetaDialog = false;
  let showAddTermMetaDialog = false;
  let newUser: Partial<User> = {
    user_firstname: '',
    user_email: '',
    user_created_at: Date.now(),
    user_type: 1
  };
  let newItem: Partial<Item> = {
    item_name: '',
    item_slug: '',
    item_status: 0,
    item_created_at: Date.now(),
    item_url: '',
    user_id: undefined
  };
  let newTerm: Partial<Term> = {
    term_name: '',
    term_slug: '',
    term_taxonomy: ''
  };
  let newOption: Partial<Option> = {
    option_key: '',
    option_value: ''
  };
  let newItemMeta: Partial<ItemMeta> = {
    item_id: undefined,
    item_meta_key: '',
    item_meta_value: ''
  };
  let newTermMeta: Partial<TermMeta> = {
    term_id: undefined,
    term_meta_key: '',
    term_meta_value: ''
  };
  
  onMount(() => {
    if (browser) {
      const savedToken = localStorage.getItem('master_token');
      if (savedToken) {
        token = savedToken;
        isAuthenticated = true;
        loadData();
      }
    }
  });
  
  async function login() {
    try {
      loading = true;
      loginError = '';
      
      const response = await fetch('/api/Login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password })
      });
      
      const result = await response.json();
      
      if (result.success) {
        token = result.data.token;
        isAuthenticated = true;
        localStorage.setItem('master_token', token);
        await loadData();
      } else {
        loginError = result.error || 'Login failed';
      }
    } catch (error) {
      loginError = 'Network error';
    } finally {
      loading = false;
    }
  }
  
  function logout() {
    isAuthenticated = false;
    token = '';
    password = '';
    localStorage.removeItem('master_token');
  }
  
  async function apiCall(endpoint: string, body?: any) {
    const options: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body || {})
    };

    const response = await fetch(endpoint, options);
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'API call failed');
    }

    return result.data;
  }
  
  async function loadData() {
    try {
      loading = true;
      dataError = '';
      showSetupMessage = false;

      const [usersData, itemsData, termsData, optionsData, itemMetasData, termMetasData] = await Promise.all([
        apiCall('/api/GetUsers'),
        apiCall('/api/GetItems'),
        apiCall('/api/GetTerms'),
        apiCall('/api/GetOptions'),
        apiCall('/api/GetItemMetas'),
        apiCall('/api/GetTermMetas')
      ]);

      users = usersData.users;
      items = itemsData.items;
      terms = termsData.terms;
      options = optionsData.options;
      itemMetas = itemMetasData.item_metas;
      termMetas = termMetasData.term_metas;
    } catch (error) {
      console.error('Failed to load data:', error);
      dataError = error instanceof Error ? error.message : 'Failed to load data';

      // Check if it's a database setup issue
      if (dataError.includes('does not exist') || dataError.includes('relation') || dataError.includes('table')) {
        showSetupMessage = true;
      }
    } finally {
      loading = false;
    }
  }
  
  async function deleteUser(userId: number) {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        await apiCall('/api/DeleteUser', { user_id: userId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('Failed to delete user');
      }
    }
  }
  
  async function deleteItem(itemId: number) {
    if (confirm('Are you sure you want to delete this item?')) {
      try {
        await apiCall('/api/DeleteItem', { item_id: itemId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete item:', error);
        alert('Failed to delete item');
      }
    }
  }

  async function deleteTerm(termId: number) {
    if (confirm('Are you sure you want to delete this term?')) {
      try {
        await apiCall('/api/DeleteTerm', { term_id: termId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete term:', error);
        alert('Failed to delete term');
      }
    }
  }

  async function deleteOption(optionId: number) {
    if (confirm('Are you sure you want to delete this option?')) {
      try {
        await apiCall('/api/DeleteOption', { option_id: optionId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete option:', error);
        alert('Failed to delete option');
      }
    }
  }

  async function deleteItemMeta(itemMetaId: number) {
    if (confirm('Are you sure you want to delete this item meta?')) {
      try {
        await apiCall('/api/DeleteItemMeta', { item_meta_id: itemMetaId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete item meta:', error);
        alert('Failed to delete item meta');
      }
    }
  }

  async function deleteTermMeta(termMetaId: number) {
    if (confirm('Are you sure you want to delete this term meta?')) {
      try {
        await apiCall('/api/DeleteTermMeta', { term_meta_id: termMetaId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete term meta:', error);
        alert('Failed to delete term meta');
      }
    }
  }

  // Add functions
  async function addUser() {
    try {
      await apiCall('/api/SaveUser', { user: newUser });
      newUser = {
        user_firstname: '',
        user_email: '',
        user_created_at: Date.now(),
        user_type: 1
      };
      showAddUserDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('Failed to add user');
    }
  }

  async function addItem() {
    try {
      await apiCall('/api/SaveItem', { item: newItem });
      newItem = {
        item_name: '',
        item_slug: '',
        item_status: 0,
        item_created_at: Date.now(),
        item_url: '',
        user_id: undefined
      };
      showAddItemDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add item:', error);
      alert('Failed to add item');
    }
  }

  async function addTerm() {
    try {
      await apiCall('/api/SaveTerm', { term: newTerm });
      newTerm = {
        term_name: '',
        term_slug: '',
        term_taxonomy: ''
      };
      showAddTermDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add term:', error);
      alert('Failed to add term');
    }
  }

  async function addOption() {
    try {
      await apiCall('/api/SaveOption', { option: newOption });
      newOption = {
        option_key: '',
        option_value: ''
      };
      showAddOptionDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add option:', error);
      alert('Failed to add option');
    }
  }

  async function addItemMeta() {
    try {
      await apiCall('/api/SaveItemMeta', { item_meta: newItemMeta });
      newItemMeta = {
        item_id: undefined,
        item_meta_key: '',
        item_meta_value: ''
      };
      showAddItemMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add item meta:', error);
      alert('Failed to add item meta');
    }
  }

  async function addTermMeta() {
    try {
      await apiCall('/api/SaveTermMeta', { term_meta: newTermMeta });
      newTermMeta = {
        term_id: undefined,
        term_meta_key: '',
        term_meta_value: ''
      };
      showAddTermMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add term meta:', error);
      alert('Failed to add term meta');
    }
  }
</script>

{#if !isAuthenticated}
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="w-full max-w-md bg-white rounded-lg shadow-md">
      <div class="p-6 border-b border-gray-100">
        <h1 class="text-xl font-semibold text-gray-900">Master Dashboard Login</h1>
        <p class="text-sm text-gray-600 mt-1">Enter the master password to access the dashboard</p>
      </div>
      <div class="p-6 space-y-4">
        <input
          type="password"
          placeholder="Master password"
          bind:value={password}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        {#if loginError}
          <p class="text-sm text-red-600">{loginError}</p>
        {/if}
        <button
          onclick={login}
          disabled={loading || !password}
          class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors disabled:cursor-not-allowed"
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
      </div>
    </div>
  </div>
{:else}
  <div class="min-h-screen bg-gray-50">
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <h1 class="text-3xl md:text-4xl font-bold text-blue-900">Master Dashboard</h1>
          <Button variant="outline" onclick={logout}>
            Logout
          </Button>
        </div>
      </div>
    </header>
    
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {#if showSetupMessage}
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Database Setup Required</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>The database tables don't exist yet. You need to set up the database first.</p>
                <div class="mt-2">
                  <a href="/setup" class="font-medium text-yellow-800 underline hover:text-yellow-600">
                    Go to Database Setup →
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      {:else if dataError}
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error Loading Data</h3>
              <div class="mt-2 text-sm text-red-700">
                <p>{dataError}</p>
                <Button
                  variant="link"
                  onclick={loadData}
                  class="mt-2 font-medium text-red-800 underline hover:text-red-600 p-0"
                >
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <Tabs bind:value={activeTab} class="w-full">
        <TabsList class="grid w-full grid-cols-6">
          <TabsTrigger value="users">Users ({users.length})</TabsTrigger>
          <TabsTrigger value="items">Items ({items.length})</TabsTrigger>
          <TabsTrigger value="terms">Terms ({terms.length})</TabsTrigger>
          <TabsTrigger value="options">Options ({options.length})</TabsTrigger>
          <TabsTrigger value="item-metas">Item Metas ({itemMetas.length})</TabsTrigger>
          <TabsTrigger value="term-metas">Term Metas ({termMetas.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="users" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Users Management</h2>
            <Dialog bind:open={showAddUserDialog}>
              <DialogTrigger>
                <Button>
                  Add New User
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New User</DialogTitle>
                  <DialogDescription>
                    Create a new user in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="user_firstname" class="text-right">Name</label>
                    <Input
                      id="user_firstname"
                      bind:value={newUser.user_firstname}
                      class="col-span-3"
                      placeholder="First name"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="user_email" class="text-right">Email</label>
                    <Input
                      id="user_email"
                      type="email"
                      bind:value={newUser.user_email}
                      class="col-span-3"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="user_type" class="text-right">Type</label>
                    <select
                      id="user_type"
                      bind:value={newUser.user_type}
                      class="col-span-3 px-3 py-2 border border-gray-300 rounded"
                    >
                      <option value={0}>Inactive</option>
                      <option value={1}>Member</option>
                      <option value={2}>Admin</option>
                    </select>
                  </div>
                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddUserDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addUser}
                    disabled={!newUser.user_firstname || !newUser.user_email}
                  >
                    Add User
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {#each users as user}
              <Card class="hover:shadow-lg transition-shadow duration-200">
                <CardContent class="p-6">
                  <div class="space-y-3">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h3 class="font-semibold text-lg text-gray-900 mb-1">{user.user_firstname}</h3>
                        <p class="text-sm text-gray-600 break-all">{user.user_email}</p>
                      </div>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        ID: {user.user_id}
                      </span>
                    </div>

                    <div class="space-y-1">
                      <div class="flex items-center gap-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {user.user_type === 0 ? 'Inactive' : user.user_type === 1 ? 'Member' : 'Admin'}
                        </span>
                      </div>
                    </div>

                    <div class="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" class="flex-1">
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        class="flex-1"
                        onclick={() => deleteUser(user.user_id!)}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>

        <TabsContent value="items" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Items Management</h2>
            <Dialog bind:open={showAddItemDialog}>
              <DialogTrigger>
                <Button
                  disabled={users.length === 0}
                  title={users.length === 0 ? "Create a user first" : ""}
                >
                  Add New Item
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Item</DialogTitle>
                  <DialogDescription>
                    Create a new item in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_name" class="text-right">Name</label>
                    <Input
                      id="item_name"
                      bind:value={newItem.item_name}
                      class="col-span-3"
                      placeholder="Item name"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_slug" class="text-right">Slug</label>
                    <Input
                      id="item_slug"
                      bind:value={newItem.item_slug}
                      class="col-span-3"
                      placeholder="item-slug"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_url" class="text-right">URL</label>
                    <Input
                      id="item_url"
                      bind:value={newItem.item_url}
                      class="col-span-3"
                      placeholder="https://example.com"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_status" class="text-right">Status</label>
                    <select
                      id="item_status"
                      bind:value={newItem.item_status}
                      class="col-span-3 px-3 py-2 border border-gray-300 rounded"
                    >
                      <option value="pending">Pending</option>
                      <option value="published">Published</option>
                      <option value="draft">Draft</option>
                    </select>
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_user_id" class="text-right">User</label>
                    <select
                      id="item_user_id"
                      bind:value={newItem.user_id}
                      class="col-span-3 px-3 py-2 border border-gray-300 rounded"
                    >
                      <option value="">Select a user (required)</option>
                      {#each users as user}
                        <option value={user.user_id}>{user.user_firstname} ({user.user_email})</option>
                      {/each}
                    </select>
                  </div>

                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddItemDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addItem}
                    disabled={!newItem.item_name || !newItem.item_slug || !newItem.user_id}
                  >
                    Add Item
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid gap-4">
            {#each items as item}
              <Card>
                <CardContent class="flex justify-between items-center p-4">
                  <div>
                    <h3 class="font-medium">{item.item_name}</h3>
                    <p class="text-sm text-gray-600">Slug: {item.item_slug}</p>
                    <p class="text-sm text-blue-600">{item.item_url || 'No URL'}</p>
                    <p class="text-xs text-gray-500">Status: {item.item_status} | User ID: {item.user_id}</p>
                    <p class="text-xs text-gray-500">ID: {item.item_id} | Created: {new Date(Number(item.item_created_at)).toLocaleDateString()}</p>
                  </div>
                  <div class="flex gap-2">
                    <Button variant="outline" size="sm">Edit</Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onclick={() => deleteItem(item.item_id!)}
                    >
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>

        <TabsContent value="terms" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Terms Management</h2>
            <Dialog bind:open={showAddTermDialog}>
              <DialogTrigger>
                <Button>
                  Add New Term
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Term</DialogTitle>
                  <DialogDescription>
                    Create a new term in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_name" class="text-right">Name</label>
                    <Input
                      id="term_name"
                      bind:value={newTerm.term_name}
                      class="col-span-3"
                      placeholder="Term name"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_slug" class="text-right">Slug</label>
                    <Input
                      id="term_slug"
                      bind:value={newTerm.term_slug}
                      class="col-span-3"
                      placeholder="term-slug"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_taxonomy" class="text-right">Taxonomy</label>
                    <Input
                      id="term_taxonomy"
                      bind:value={newTerm.term_taxonomy}
                      class="col-span-3"
                      placeholder="category, tag, etc."
                    />
                  </div>

                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddTermDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addTerm}
                    disabled={!newTerm.term_name || !newTerm.term_slug || !newTerm.term_taxonomy}
                  >
                    Add Term
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid gap-4">
            {#each terms as term}
              <Card>
                <CardContent class="flex justify-between items-center p-4">
                  <div>
                    <h3 class="font-medium">{term.term_name}</h3>
                    <p class="text-sm text-gray-600">Slug: {term.term_slug}</p>
                    <p class="text-xs text-gray-500">Taxonomy: {term.term_taxonomy}</p>
                    <p class="text-xs text-gray-500">ID: {term.term_id}</p>
                  </div>
                  <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Edit</button>
                    <button
                      class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                      onclick={() => deleteTerm(term.term_id!)}
                    >
                      Delete
                    </button>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>

        <TabsContent value="options" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Options Management</h2>
            <Dialog bind:open={showAddOptionDialog}>
              <DialogTrigger>
                <Button>
                  Add New Option
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Option</DialogTitle>
                  <DialogDescription>
                    Create a new option in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="option_key" class="text-right">Key</label>
                    <Input
                      id="option_key"
                      bind:value={newOption.option_key}
                      class="col-span-3"
                      placeholder="option_key"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="option_value" class="text-right">Value</label>
                    <Input
                      id="option_value"
                      bind:value={newOption.option_value}
                      class="col-span-3"
                      placeholder="Option value"
                    />
                  </div>

                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddOptionDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addOption}
                    disabled={!newOption.option_key || !newOption.option_value}
                  >
                    Add Option
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid gap-4">
            {#each options as option}
              <Card>
                <CardContent class="flex justify-between items-center p-4">
                  <div>
                    <h3 class="font-medium">{option.option_key}</h3>
                    <p class="text-sm text-gray-600">{option.option_value || 'No value'}</p>

                    <p class="text-xs text-gray-500">ID: {option.option_id}</p>
                  </div>
                  <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Edit</button>
                    <button
                      class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                      onclick={() => deleteOption(option.option_id!)}
                    >
                      Delete
                    </button>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>

        <TabsContent value="item-metas" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Item Metas Management</h2>
            <Dialog bind:open={showAddItemMetaDialog}>
              <DialogTrigger>
                <Button
                  disabled={items.length === 0}
                  title={items.length === 0 ? "Create an item first before adding item metas" : ""}
                >
                  Add New Item Meta
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Item Meta</DialogTitle>
                  <DialogDescription>
                    Create a new item meta in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_meta_item_id" class="text-right">Item</label>
                    <select
                      id="item_meta_item_id"
                      bind:value={newItemMeta.item_id}
                      class="col-span-3 px-3 py-2 border border-gray-300 rounded"
                    >
                      <option value="">Select an item (required)</option>
                      {#each items as item}
                        <option value={item.item_id}>{item.item_name} (ID: {item.item_id})</option>
                      {/each}
                    </select>
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_meta_key" class="text-right">Key</label>
                    <Input
                      id="item_meta_key"
                      bind:value={newItemMeta.item_meta_key}
                      class="col-span-3"
                      placeholder="meta_key"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="item_meta_value" class="text-right">Value</label>
                    <Input
                      id="item_meta_value"
                      bind:value={newItemMeta.item_meta_value}
                      class="col-span-3"
                      placeholder="Meta value"
                    />
                  </div>
                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddItemMetaDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addItemMeta}
                    disabled={!newItemMeta.item_meta_key || !newItemMeta.item_id}
                  >
                    Add Item Meta
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid gap-4">
            {#each itemMetas as itemMeta}
              <Card>
                <CardContent class="flex justify-between items-center p-4">
                  <div>
                    <h3 class="font-medium">{itemMeta.item_meta_key}</h3>
                    <p class="text-sm text-gray-600">{itemMeta.item_meta_value || 'No value'}</p>
                    <p class="text-xs text-gray-500">Item ID: {itemMeta.item_id}</p>
                    <p class="text-xs text-gray-500">ID: {itemMeta.item_meta_id}</p>
                  </div>
                  <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Edit</button>
                    <button
                      class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                      onclick={() => deleteItemMeta(itemMeta.item_meta_id!)}
                    >
                      Delete
                    </button>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>

        <TabsContent value="term-metas" class="space-y-4">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Term Metas Management</h2>
            <Dialog bind:open={showAddTermMetaDialog}>
              <DialogTrigger>
                <Button
                  disabled={terms.length === 0}
                  title={terms.length === 0 ? "Create a term first before adding term metas" : ""}
                >
                  Add New Term Meta
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Term Meta</DialogTitle>
                  <DialogDescription>
                    Create a new term meta in the system.
                  </DialogDescription>
                </DialogHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_meta_term_id" class="text-right">Term</label>
                    <select
                      id="term_meta_term_id"
                      bind:value={newTermMeta.term_id}
                      class="col-span-3 px-3 py-2 border border-gray-300 rounded"
                    >
                      <option value="">Select a term (required)</option>
                      {#each terms as term}
                        <option value={term.term_id}>{term.term_name} ({term.term_taxonomy}) - ID: {term.term_id}</option>
                      {/each}
                    </select>
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_meta_key" class="text-right">Key</label>
                    <Input
                      id="term_meta_key"
                      bind:value={newTermMeta.term_meta_key}
                      class="col-span-3"
                      placeholder="meta_key"
                    />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <label for="term_meta_value" class="text-right">Value</label>
                    <Input
                      id="term_meta_value"
                      bind:value={newTermMeta.term_meta_value}
                      class="col-span-3"
                      placeholder="Meta value"
                    />
                  </div>
                </div>
                <div class="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onclick={() => showAddTermMetaDialog = false}
                  >
                    Cancel
                  </Button>
                  <Button
                    onclick={addTermMeta}
                    disabled={!newTermMeta.term_meta_key || !newTermMeta.term_id}
                  >
                    Add Term Meta
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div class="grid gap-4">
            {#each termMetas as termMeta}
              <Card>
                <CardContent class="flex justify-between items-center p-4">
                  <div>
                    <h3 class="font-medium">{termMeta.term_meta_key}</h3>
                    <p class="text-sm text-gray-600">{termMeta.term_meta_value || 'No value'}</p>
                    <p class="text-xs text-gray-500">Term ID: {termMeta.term_id}</p>
                    <p class="text-xs text-gray-500">ID: {termMeta.term_meta_id}</p>
                  </div>
                  <div class="flex gap-2">
                    <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Edit</button>
                    <button
                      class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                      onclick={() => deleteTermMeta(termMeta.term_meta_id!)}
                    >
                      Delete
                    </button>
                  </div>
                </CardContent>
              </Card>
            {/each}
          </div>
        </TabsContent>
      </Tabs>
    </main>
  </div>
{/if}
